import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
from ray.rllib.models import ModelCatalog
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.torch.fcnet import FullyConnectedNetwork
import torch
import torch.nn as nn
import numpy as np
import os
import json
from datetime import datetime

from chimera_env import ChimeraEnv
from chimera_model import ChimeraCNN


class ChimeraRLlibModel(TorchModelV2, nn.Module):
    """
    将ChimeraCNN包装为RLlib兼容的模型
    """
    
    def __init__(self, obs_space, action_space, num_outputs, model_config, name):
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # 创建ChimeraCNN模型
        self.chimera_model = ChimeraCNN(
            input_channels=1,
            hidden_dim=model_config.get("hidden_dim", 128)
        )
        
        # RLlib需要的输出维度
        self._num_outputs = num_outputs
        
    def forward(self, input_dict, state, seq_lens):
        """RLlib模型的前向传播"""
        obs = input_dict["obs"].float()
        
        # 获取动作logits和价值
        action_logits, value = self.chimera_model(obs)
        
        # 存储价值用于价值函数
        self._value = value.squeeze(-1)
        
        return action_logits, state
    
    def value_function(self):
        """返回状态价值"""
        return self._value


def create_env(config=None):
    """创建环境的工厂函数"""
    return ChimeraEnv()


def setup_training():
    """设置训练配置"""
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    # 注册环境
    register_env("chimera_v1", create_env)
    
    # 注册自定义模型
    ModelCatalog.register_custom_model("chimera_cnn", ChimeraRLlibModel)
    
    # 创建PPO配置
    config = (
        PPOConfig()
        .environment(env="chimera_v1")
        .framework("torch")
        .training(
            # PPO超参数
            lr=3e-4,
            gamma=0.99,
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            train_batch_size=4000,
            sgd_minibatch_size=128,
            num_sgd_iter=10,
            # 模型配置
            model={
                "custom_model": "chimera_cnn",
                "custom_model_config": {
                    "hidden_dim": 128,
                },
            },
        )
        .rollouts(
            num_rollout_workers=2,
            rollout_fragment_length=200,
        )
        .multi_agent(
            # 自对弈配置：两个智能体使用同一个策略
            policies={
                "shared_policy": (
                    None,  # 使用默认策略类
                    ChimeraEnv().observation_spaces["player_0"],
                    ChimeraEnv().action_spaces["player_0"],
                    {}
                )
            },
            policy_mapping_fn=lambda agent_id, episode, worker, **kwargs: "shared_policy",
            policies_to_train=["shared_policy"],
        )
        .resources(
            num_gpus=0,  # 如果有GPU可以设置为1
        )
        .debugging(
            log_level="INFO",
        )
    )
    
    return config


def train_chimera(num_iterations=100, checkpoint_freq=10):
    """
    训练Chimera智能体
    
    Args:
        num_iterations: 训练迭代次数
        checkpoint_freq: 保存检查点的频率
    """
    print("🔥 启动Project Chimera训练...")
    print(f"训练迭代次数: {num_iterations}")
    print(f"检查点保存频率: 每{checkpoint_freq}次迭代")
    
    # 设置训练配置
    config = setup_training()
    
    # 构建算法
    algo = config.build()
    
    # 创建结果保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"chimera_results_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    
    # 训练循环
    best_reward = float('-inf')
    training_results = []
    
    try:
        for iteration in range(1, num_iterations + 1):
            print(f"\n🚀 训练迭代 {iteration}/{num_iterations}")
            
            # 执行一次训练
            result = algo.train()
            
            # 提取关键指标
            episode_reward_mean = result.get("episode_reward_mean", 0)
            episode_len_mean = result.get("episode_len_mean", 0)
            policy_reward = result.get("policy_reward_mean", {}).get("shared_policy", 0)
            
            print(f"📊 平均回合奖励: {episode_reward_mean:.2f}")
            print(f"📏 平均回合长度: {episode_len_mean:.1f}")
            print(f"🎯 策略奖励: {policy_reward:.2f}")
            
            # 记录训练结果
            training_results.append({
                "iteration": iteration,
                "episode_reward_mean": episode_reward_mean,
                "episode_len_mean": episode_len_mean,
                "policy_reward": policy_reward,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保存最佳模型
            if episode_reward_mean > best_reward:
                best_reward = episode_reward_mean
                best_checkpoint = algo.save(os.path.join(results_dir, "best_model"))
                print(f"💎 新的最佳模型! 奖励: {best_reward:.2f}, 保存至: {best_checkpoint}")
            
            # 定期保存检查点
            if iteration % checkpoint_freq == 0:
                checkpoint = algo.save(os.path.join(results_dir, f"checkpoint_{iteration}"))
                print(f"💾 检查点已保存: {checkpoint}")
                
                # 保存训练结果
                with open(os.path.join(results_dir, "training_results.json"), "w") as f:
                    json.dump(training_results, f, indent=2)
    
    except KeyboardInterrupt:
        print("\n⏹️ 训练被用户中断")
    
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
    
    finally:
        # 保存最终检查点
        final_checkpoint = algo.save(os.path.join(results_dir, "final_model"))
        print(f"🏁 最终模型已保存: {final_checkpoint}")
        
        # 保存完整的训练结果
        with open(os.path.join(results_dir, "training_results.json"), "w") as f:
            json.dump(training_results, f, indent=2)
        
        # 清理资源
        algo.stop()
        ray.shutdown()
        
        print(f"✅ 训练完成! 结果保存在: {results_dir}")
        print(f"🏆 最佳平均奖励: {best_reward:.2f}")


def test_environment():
    """测试环境是否正常工作"""
    print("🧪 测试Chimera环境...")
    
    env = ChimeraEnv()
    obs, info = env.reset()
    
    print(f"观察空间: {env.observation_spaces}")
    print(f"动作空间: {env.action_spaces}")
    print(f"初始观察形状: {obs['player_0'].shape}")
    
    # 测试几步随机动作
    for step in range(5):
        actions = {
            agent: env.action_spaces[agent].sample() 
            for agent in env.agents
        }
        obs, rewards, terminations, truncations, infos = env.step(actions)
        print(f"步骤 {step + 1}: 奖励 = {rewards}, 终止 = {terminations}")
        
        if any(terminations.values()) or any(truncations.values()):
            break
    
    env.close()
    print("✅ 环境测试完成")


if __name__ == "__main__":
    # 首先测试环境
    test_environment()
    
    # 开始训练
    train_chimera(num_iterations=100, checkpoint_freq=10)

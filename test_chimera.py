#!/usr/bin/env python3
"""
Project Chimera 测试脚本
测试环境和模型的基本功能
"""

import numpy as np
import torch
import pygame
import time
import random

from chimera_env import ChimeraEnv
from chimera_model import ChimeraCNN


def test_environment():
    """测试环境基本功能"""
    print("🧪 测试Chimera环境...")
    
    env = ChimeraEnv()
    
    # 测试重置
    obs, info = env.reset(seed=42)
    print(f"✅ 环境重置成功")
    print(f"观察空间: {env.observation_spaces}")
    print(f"动作空间: {env.action_spaces}")
    print(f"初始观察形状: {obs['player_0'].shape}")
    print(f"初始分数: {env.scores}")
    
    # 打印初始网格状态
    print("\n初始网格状态:")
    print(obs['player_0'])
    
    # 测试几步随机动作
    print("\n🎮 测试随机动作...")
    for step in range(10):
        actions = {
            agent: env.action_spaces[agent].sample() 
            for agent in env.agents
        }
        
        print(f"\n步骤 {step + 1}:")
        print(f"动作: {actions}")
        
        obs, rewards, terminations, truncations, infos = env.step(actions)
        
        print(f"奖励: {rewards}")
        print(f"分数: {infos}")
        print(f"终止: {terminations}")
        print(f"截断: {truncations}")
        
        if any(terminations.values()) or any(truncations.values()):
            print("🏁 游戏结束!")
            break
    
    env.close()
    print("✅ 环境测试完成")
    return True


def test_model():
    """测试模型基本功能"""
    print("\n🧠 测试Chimera模型...")
    
    model = ChimeraCNN()
    print(f"✅ 模型创建成功")
    print(f"参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建测试输入
    batch_size = 4
    test_input = torch.randint(0, 4, (batch_size, 10, 10)).float()
    
    print(f"\n测试输入形状: {test_input.shape}")
    
    # 测试前向传播
    with torch.no_grad():
        action_logits, state_value = model(test_input)
        print(f"✅ 前向传播成功")
        print(f"动作logits形状: {action_logits.shape}")
        print(f"状态价值形状: {state_value.shape}")
        
        # 测试动作采样
        action, log_prob, entropy, value = model.get_action_and_value(test_input)
        print(f"✅ 动作采样成功")
        print(f"采样动作: {action}")
        print(f"动作形状: {action.shape}")
        print(f"Log概率形状: {log_prob.shape}")
        print(f"熵形状: {entropy.shape}")
        print(f"价值形状: {value.shape}")
    
    print("✅ 模型测试完成")
    return True


def test_integration():
    """测试环境和模型的集成"""
    print("\n🔗 测试环境-模型集成...")
    
    env = ChimeraEnv()
    model = ChimeraCNN()
    model.eval()
    
    obs, info = env.reset(seed=42)
    
    print("🎯 使用模型进行决策...")
    
    for step in range(5):
        # 将观察转换为模型输入
        model_inputs = {}
        for agent in env.agents:
            obs_tensor = torch.FloatTensor(obs[agent]).unsqueeze(0)  # 添加batch维度
            model_inputs[agent] = obs_tensor
        
        # 使用模型生成动作
        actions = {}
        with torch.no_grad():
            for agent in env.agents:
                action, _, _, _ = model.get_action_and_value(model_inputs[agent])
                actions[agent] = action.item()
        
        print(f"\n步骤 {step + 1}:")
        print(f"模型动作: {actions}")
        
        # 执行动作
        obs, rewards, terminations, truncations, infos = env.step(actions)
        
        print(f"奖励: {rewards}")
        print(f"分数: {infos}")
        
        if any(terminations.values()) or any(truncations.values()):
            print("🏁 游戏结束!")
            break
    
    env.close()
    print("✅ 集成测试完成")
    return True


def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示 (按ESC退出)")
    
    env = ChimeraEnv()
    model = ChimeraCNN()
    model.eval()
    
    obs, info = env.reset()
    
    # 动作映射
    action_names = {
        0: "向上",
        1: "向下", 
        2: "向左",
        3: "向右",
        4: "采集/攻击",
        5: "不动"
    }
    
    running = True
    clock = pygame.time.Clock()
    
    try:
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        # 空格键执行一步
                        model_inputs = {}
                        for agent in env.agents:
                            obs_tensor = torch.FloatTensor(obs[agent]).unsqueeze(0)
                            model_inputs[agent] = obs_tensor
                        
                        actions = {}
                        with torch.no_grad():
                            for agent in env.agents:
                                action, _, _, _ = model.get_action_and_value(model_inputs[agent])
                                actions[agent] = action.item()
                        
                        print(f"动作: {[action_names[a] for a in actions.values()]}")
                        
                        obs, rewards, terminations, truncations, infos = env.step(actions)
                        
                        print(f"奖励: {rewards}")
                        print(f"分数: {infos}")
                        
                        if any(terminations.values()) or any(truncations.values()):
                            print("🏁 游戏结束! 按R重新开始")
                            
                    elif event.key == pygame.K_r:
                        # R键重新开始
                        obs, info = env.reset()
                        print("🔄 游戏重新开始")
            
            # 渲染
            env.render()
            clock.tick(60)
            
    except KeyboardInterrupt:
        print("\n演示被中断")
    
    finally:
        env.close()
        pygame.quit()


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始Project Chimera测试套件...")
    
    tests = [
        ("环境测试", test_environment),
        ("模型测试", test_model),
        ("集成测试", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"运行: {test_name}")
            print('='*50)
            
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 出现异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过! Project Chimera准备就绪!")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    # 运行所有测试
    if run_all_tests():
        # 如果所有测试通过，提供交互式演示选项
        print("\n是否要运行交互式演示? (y/n)")
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            print("提示: 按空格键执行一步，按R重新开始，按ESC退出")
            interactive_demo()
    
    print("\n🏁 测试完成!")

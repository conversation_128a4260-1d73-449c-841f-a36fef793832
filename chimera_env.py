import numpy as np
import pygame
from pettingzoo import ParallelEnv
from gymnasium import spaces
import random


class ChimeraEnv(ParallelEnv):
    """
    Project Chimera - 双智能体资源争夺环境
    两个AI智能体在10x10网格中争夺资源，消灭对方或采集更多资源者获胜
    """
    
    metadata = {"render_modes": ["human"], "name": "chimera_v1"}
    
    def __init__(self):
        """初始化环境"""
        # 定义智能体
        self.possible_agents = ["player_0", "player_1"]
        self.agents = self.possible_agents[:]
        
        # 网格设置
        self.grid_size = 10
        self.max_steps = 200
        
        # 观察空间：10x10网格，每个位置的值表示不同的对象
        # 0: 空地, 1: player_0, 2: player_1, 3: 资源
        self.observation_spaces = {
            agent: spaces.Box(
                low=0, high=3, shape=(self.grid_size, self.grid_size), dtype=np.int32
            ) for agent in self.possible_agents
        }
        
        # 动作空间：6个离散动作
        # 0: 向上移动, 1: 向下移动, 2: 向左移动, 3: 向右移动, 4: 采集/攻击, 5: 什么都不做
        self.action_spaces = {
            agent: spaces.Discrete(6) for agent in self.possible_agents
        }
        
        # Pygame初始化
        pygame.init()
        self.screen_size = 600
        self.screen = pygame.display.set_mode((self.screen_size, self.screen_size))
        pygame.display.set_caption("Project Chimera")
        self.clock = pygame.time.Clock()
        
        # 颜色定义
        self.colors = {
            0: (128, 128, 128),  # 空地 - 灰色
            1: (0, 0, 255),      # player_0 - 蓝色
            2: (255, 0, 0),      # player_1 - 红色
            3: (0, 255, 0),      # 资源 - 绿色
        }
        
        # 游戏状态
        self.grid = None
        self.agent_positions = {}
        self.scores = {}
        self.step_count = 0
        
    def reset(self, seed=None, options=None):
        """重置环境到初始状态"""
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
            
        # 重置游戏状态
        self.agents = self.possible_agents[:]
        self.grid = np.zeros((self.grid_size, self.grid_size), dtype=np.int32)
        self.agent_positions = {}
        self.scores = {agent: 0 for agent in self.agents}
        self.step_count = 0
        
        # 随机放置智能体
        available_positions = [(i, j) for i in range(self.grid_size) for j in range(self.grid_size)]
        
        # 放置player_0
        pos_0 = random.choice(available_positions)
        available_positions.remove(pos_0)
        self.agent_positions["player_0"] = pos_0
        self.grid[pos_0[0], pos_0[1]] = 1
        
        # 放置player_1
        pos_1 = random.choice(available_positions)
        available_positions.remove(pos_1)
        self.agent_positions["player_1"] = pos_1
        self.grid[pos_1[0], pos_1[1]] = 2
        
        # 随机放置5个资源
        for _ in range(5):
            if available_positions:
                resource_pos = random.choice(available_positions)
                available_positions.remove(resource_pos)
                self.grid[resource_pos[0], resource_pos[1]] = 3
        
        # 返回初始观察
        observations = {agent: self._get_obs(agent) for agent in self.agents}
        infos = {agent: {} for agent in self.agents}
        
        return observations, infos
    
    def step(self, actions):
        """执行一步动作"""
        self.step_count += 1
        rewards = {agent: -0.1 for agent in self.agents}  # 默认每步-0.1奖励鼓励效率
        terminations = {agent: False for agent in self.agents}
        truncations = {agent: False for agent in self.agents}

        # 处理每个智能体的动作
        for agent, action in actions.items():
            if agent not in self.agents:
                continue

            current_pos = self.agent_positions[agent]
            new_pos = current_pos

            # 移动动作
            if action == 0:  # 向上移动
                new_pos = (max(0, current_pos[0] - 1), current_pos[1])
            elif action == 1:  # 向下移动
                new_pos = (min(self.grid_size - 1, current_pos[0] + 1), current_pos[1])
            elif action == 2:  # 向左移动
                new_pos = (current_pos[0], max(0, current_pos[1] - 1))
            elif action == 3:  # 向右移动
                new_pos = (current_pos[0], min(self.grid_size - 1, current_pos[1] + 1))
            elif action == 4:  # 采集/攻击
                # 检查当前位置是否有资源
                if self.grid[current_pos[0], current_pos[1]] == 3:
                    # 采集资源
                    self.grid[current_pos[0], current_pos[1]] = 1 if agent == "player_0" else 2
                    self.scores[agent] += 1
                    rewards[agent] += 10  # 采集奖励
                else:
                    # 检查是否攻击对手
                    opponent = "player_1" if agent == "player_0" else "player_0"
                    opponent_pos = self.agent_positions[opponent]
                    if current_pos == opponent_pos:
                        # 攻击成功，游戏结束
                        rewards[agent] += 50  # 胜利奖励
                        rewards[opponent] -= 50  # 失败惩罚
                        terminations[agent] = True
                        terminations[opponent] = True
            # action == 5: 什么都不做

            # 更新位置（如果是移动动作且目标位置为空）
            if action in [0, 1, 2, 3]:
                if self.grid[new_pos[0], new_pos[1]] == 0:  # 目标位置为空
                    # 清除原位置
                    self.grid[current_pos[0], current_pos[1]] = 0
                    # 设置新位置
                    self.grid[new_pos[0], new_pos[1]] = 1 if agent == "player_0" else 2
                    self.agent_positions[agent] = new_pos

        # 检查游戏是否因步数达到上限而结束
        if self.step_count >= self.max_steps:
            for agent in self.agents:
                truncations[agent] = True

            # 根据分数决定胜负
            if self.scores["player_0"] > self.scores["player_1"]:
                rewards["player_0"] += 20
                rewards["player_1"] -= 20
            elif self.scores["player_1"] > self.scores["player_0"]:
                rewards["player_1"] += 20
                rewards["player_0"] -= 20
            # 平局时不额外奖励

        observations = {agent: self._get_obs(agent) for agent in self.agents}
        infos = {agent: {"score": self.scores[agent]} for agent in self.agents}

        return observations, rewards, terminations, truncations, infos
    
    def render(self, mode="human"):
        """渲染游戏画面"""
        if mode != "human":
            return

        # 清空屏幕
        self.screen.fill((255, 255, 255))  # 白色背景

        # 计算每个网格单元的大小
        cell_size = self.screen_size // self.grid_size

        # 绘制网格
        for i in range(self.grid_size):
            for j in range(self.grid_size):
                rect = pygame.Rect(j * cell_size, i * cell_size, cell_size, cell_size)
                color = self.colors[self.grid[i, j]]
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, (0, 0, 0), rect, 1)  # 黑色边框

        # 显示分数
        font = pygame.font.Font(None, 36)
        score_text = f"Player 0: {self.scores.get('player_0', 0)}  Player 1: {self.scores.get('player_1', 0)}  Steps: {self.step_count}"
        text_surface = font.render(score_text, True, (0, 0, 0))
        self.screen.blit(text_surface, (10, self.screen_size - 40))

        pygame.display.flip()
        self.clock.tick(60)
    
    def _get_obs(self, agent):
        """获取指定智能体的观察"""
        # 目前返回完整的网格作为观察
        return self.grid.copy()
    
    def close(self):
        """关闭环境"""
        if hasattr(self, 'screen'):
            pygame.quit()

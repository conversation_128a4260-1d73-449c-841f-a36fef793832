# Project Chimera 🔥

一个基于强化学习的双智能体对战游戏，两个AI智能体在10x10网格中争夺资源。

## 🎯 游戏目标

- **胜利条件**: 消灭对方智能体或在规定步数内采集更多资源
- **环境**: 10x10网格世界
- **智能体**: 2个AI智能体 (player_0 vs player_1)
- **资源**: 5个随机分布的资源点

## 🛠️ 技术栈

- **环境框架**: Petting<PERSON>oo (多智能体环境)
- **渲染引擎**: Pygame (实时可视化)
- **深度学习**: PyTorch (神经网络模型)
- **强化学习**: Ray RLlib (PPO算法训练)

## 📁 项目结构

```
Project Chimera/
├── chimera_env.py      # 游戏环境实现
├── chimera_model.py    # CNN神经网络模型
├── train_chimera.py    # RLlib训练脚本
├── test_chimera.py     # 测试和演示脚本
├── requirements.txt    # 依赖包列表
└── README.md          # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 测试环境

```bash
python test_chimera.py
```

### 3. 开始训练

```bash
python train_chimera.py
```

## 🎮 游戏规则

### 动作空间
- **0**: 向上移动
- **1**: 向下移动  
- **2**: 向左移动
- **3**: 向右移动
- **4**: 采集资源/攻击对手
- **5**: 什么都不做

### 奖励系统
- **移动**: -0.1 (鼓励效率)
- **采集资源**: +10
- **消灭对手**: +50 (胜利者) / -50 (失败者)
- **步数耗尽**: +20 (高分者) / -20 (低分者)

### 游戏元素
- **空地** (灰色): 可移动区域
- **Player 0** (蓝色): 第一个智能体
- **Player 1** (红色): 第二个智能体  
- **资源** (绿色): 可采集的资源点

## 🧠 AI模型架构

### ChimeraCNN
- **输入**: 10x10网格观察
- **卷积层**: 3层CNN (16→32→64通道)
- **全连接层**: 共享特征提取
- **输出头**:
  - 策略头: 6个动作的概率分布
  - 价值头: 状态价值估计

## 🏋️ 训练配置

### PPO算法参数
- **学习率**: 3e-4
- **折扣因子**: 0.99
- **GAE λ**: 0.95
- **裁剪参数**: 0.2
- **熵系数**: 0.01

### 自对弈设置
- 两个智能体共享同一个策略网络
- 通过自对弈不断提升策略质量
- 每10次迭代保存检查点

## 📊 训练监控

训练过程中会输出以下指标：
- **平均回合奖励**: 智能体表现的主要指标
- **平均回合长度**: 游戏持续时间
- **策略奖励**: 共享策略的奖励

## 🎯 使用示例

### 基础测试
```python
from chimera_env import ChimeraEnv

env = ChimeraEnv()
obs, info = env.reset()

# 随机动作测试
for _ in range(10):
    actions = {agent: env.action_spaces[agent].sample() for agent in env.agents}
    obs, rewards, terms, truncs, infos = env.step(actions)
    env.render()
```

### 模型推理
```python
from chimera_model import ChimeraCNN
import torch

model = ChimeraCNN()
obs_tensor = torch.FloatTensor(obs['player_0']).unsqueeze(0)
action, log_prob, entropy, value = model.get_action_and_value(obs_tensor)
```

## 🔧 自定义配置

### 修改网格大小
在 `chimera_env.py` 中修改 `self.grid_size`

### 调整资源数量
在 `reset()` 方法中修改资源放置循环

### 更改奖励设置
在 `step()` 方法中修改奖励值

## 📈 性能优化

- 使用GPU加速训练 (在train_chimera.py中设置num_gpus=1)
- 调整并行worker数量
- 优化网络架构参数

## 🐛 故障排除

### 常见问题
1. **Pygame初始化失败**: 确保安装了正确版本的pygame
2. **Ray初始化错误**: 检查Ray版本兼容性
3. **CUDA错误**: 确保PyTorch CUDA版本匹配

### 调试模式
设置环境变量启用详细日志：
```bash
export RAY_LOG_LEVEL=DEBUG
python train_chimera.py
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进Project Chimera！

## 📄 许可证

MIT License

---

**Project Chimera** - 让AI智能体在虚拟世界中展开史诗对决！ 🔥⚔️🏆

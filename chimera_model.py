import torch
import torch.nn as nn
import torch.nn.functional as F


class ChimeraCNN(nn.Module):
    """
    Project Chimera的CNN模型
    处理10x10网格观察，输出动作策略和状态价值
    """
    
    def __init__(self, input_channels=1, hidden_dim=128):
        """
        初始化CNN模型
        
        Args:
            input_channels: 输入通道数，默认为1（单通道网格）
            hidden_dim: 隐藏层维度
        """
        super(ChimeraCNN, self).__init__()
        
        # 卷积层块
        self.conv_block = nn.Sequential(
            # 第一个卷积层：1 -> 16通道
            nn.Conv2d(input_channels, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(16),
            
            # 第二个卷积层：16 -> 32通道
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(32),
            
            # 第三个卷积层：32 -> 64通道
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(64),
        )
        
        # 展平层
        self.flatten = nn.Flatten()
        
        # 计算展平后的特征维度：64 * 10 * 10 = 6400
        self.feature_dim = 64 * 10 * 10
        
        # 全连接层
        self.fc_shared = nn.Sequential(
            nn.Linear(self.feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
        )
        
        # 策略头：输出6个动作的概率分布
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 6)  # 6个动作：上下左右、采集/攻击、不动
        )
        
        # 价值头：输出状态价值
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm2d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 (batch_size, 10, 10) 或 (batch_size, 1, 10, 10)
            
        Returns:
            action_logits: 动作logits，形状为 (batch_size, 6)
            state_value: 状态价值，形状为 (batch_size, 1)
        """
        # 确保输入有正确的维度
        if len(x.shape) == 3:  # (batch_size, 10, 10)
            x = x.unsqueeze(1)  # 添加通道维度 -> (batch_size, 1, 10, 10)
        elif len(x.shape) == 2:  # (10, 10)
            x = x.unsqueeze(0).unsqueeze(0)  # -> (1, 1, 10, 10)
        
        # 卷积特征提取
        conv_features = self.conv_block(x)
        
        # 展平
        flattened = self.flatten(conv_features)
        
        # 共享全连接层
        shared_features = self.fc_shared(flattened)
        
        # 策略头和价值头
        action_logits = self.policy_head(shared_features)
        state_value = self.value_head(shared_features)
        
        return action_logits, state_value
    
    def get_action_and_value(self, x, action=None):
        """
        获取动作和价值，用于训练
        
        Args:
            x: 观察张量
            action: 如果提供，计算该动作的log概率
            
        Returns:
            action: 采样的动作
            log_prob: 动作的log概率
            entropy: 策略熵
            value: 状态价值
        """
        action_logits, value = self.forward(x)
        
        # 创建动作分布
        action_dist = torch.distributions.Categorical(logits=action_logits)
        
        if action is None:
            # 采样动作
            action = action_dist.sample()
        
        # 计算log概率和熵
        log_prob = action_dist.log_prob(action)
        entropy = action_dist.entropy()
        
        return action, log_prob, entropy, value.squeeze(-1)
    
    def get_value(self, x):
        """
        仅获取状态价值
        
        Args:
            x: 观察张量
            
        Returns:
            value: 状态价值
        """
        _, value = self.forward(x)
        return value.squeeze(-1)


def create_model(input_channels=1, hidden_dim=128):
    """
    创建ChimeraCNN模型的工厂函数
    
    Args:
        input_channels: 输入通道数
        hidden_dim: 隐藏层维度
        
    Returns:
        model: ChimeraCNN模型实例
    """
    return ChimeraCNN(input_channels=input_channels, hidden_dim=hidden_dim)


if __name__ == "__main__":
    # 测试模型
    model = create_model()
    
    # 创建测试输入
    test_input = torch.randint(0, 4, (2, 10, 10)).float()  # 批次大小为2的测试输入
    
    print("模型结构:")
    print(model)
    print(f"\n参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    with torch.no_grad():
        action_logits, state_value = model(test_input)
        print(f"\n输入形状: {test_input.shape}")
        print(f"动作logits形状: {action_logits.shape}")
        print(f"状态价值形状: {state_value.shape}")
        
        # 测试动作采样
        action, log_prob, entropy, value = model.get_action_and_value(test_input)
        print(f"\n采样动作: {action}")
        print(f"Log概率: {log_prob}")
        print(f"熵: {entropy}")
        print(f"价值: {value}")
